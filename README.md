# JustCooked - Recipe Management Application

A modern digital cookbook application built with <PERSON><PERSON>, <PERSON><PERSON>, and TypeScript. JustCooked helps you import, organize, and manage your recipes with advanced ingredient parsing and local storage.

## Features

- **Recipe Import**: Import recipes from popular cooking websites
- **Smart Ingredient Parsing**: Advanced ingredient parsing using the ingredient crate with regex fallback
- **Local Storage**: All data stored locally using SQLite
- **Image Management**: Local image storage and optimization
- **Search & Filter**: Advanced recipe search and filtering capabilities
- **Responsive Design**: Works on desktop and mobile devices
- **Dark/Light Theme**: Toggle between dark and light themes

## Technology Stack

- **Frontend**: React + TypeScript + Material-UI
- **Backend**: Rust + Tauri
- **Database**: SQLite
- **Ingredient Parsing**: [ingredient crate](https://github.com/nickysemenza/ingredient-parser)

## Recommended IDE Setup

- [VS Code](https://code.visualstudio.com/) + [Tauri](https://marketplace.visualstudio.com/items?itemName=tauri-apps.tauri-vscode) + [rust-analyzer](https://marketplace.visualstudio.com/items?itemName=rust-lang.rust-analyzer)
